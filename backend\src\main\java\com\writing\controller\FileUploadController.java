package com.writing.controller;

import com.writing.common.Result;
import com.writing.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/upload")
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;

    /**
     * 上传图片
     */
    @PostMapping("/image")
    public Result<Map<String, String>> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            log.info("开始上传图片: {}", file.getOriginalFilename());

            String imageUrl = fileUploadService.uploadImage(file);

            Map<String, String> result = new HashMap<>();
            result.put("url", imageUrl);
            result.put("filename", file.getOriginalFilename());

            log.info("图片上传成功: {}", imageUrl);
            return Result.success("图片上传成功", result);

        } catch (Exception e) {
            log.error("图片上传失败", e);
            return Result.error("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传小说封面
     */
    @PostMapping("/cover")
    public Result<Map<String, String>> uploadCover(@RequestParam("file") MultipartFile file) {
        try {
            log.info("开始上传小说封面: {}", file.getOriginalFilename());

            String coverUrl = fileUploadService.uploadImage(file);

            Map<String, String> result = new HashMap<>();
            result.put("url", coverUrl);
            result.put("filename", file.getOriginalFilename());

            log.info("小说封面上传成功: {}", coverUrl);
            return Result.success("封面上传成功", result);

        } catch (Exception e) {
            log.error("封面上传失败", e);
            return Result.error("封面上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文档
     */
    @PostMapping("/document")
    public Result<Map<String, String>> uploadDocument(@RequestParam("file") MultipartFile file) {
        try {
            log.info("开始上传文档: {}", file.getOriginalFilename());

            String documentUrl = fileUploadService.uploadDocument(file);

            Map<String, String> result = new HashMap<>();
            result.put("url", documentUrl);
            result.put("filename", file.getOriginalFilename());

            log.info("文档上传成功: {}", documentUrl);
            return Result.success("文档上传成功", result);

        } catch (Exception e) {
            log.error("文档上传失败", e);
            return Result.error("文档上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/file")
    public Result<Void> deleteFile(@RequestParam("url") String fileUrl) {
        try {
            log.info("开始删除文件: {}", fileUrl);

            fileUploadService.deleteFile(fileUrl);

            log.info("文件删除成功: {}", fileUrl);
            return Result.success();

        } catch (Exception e) {
            log.error("文件删除失败", e);
            return Result.error("文件删除失败: " + e.getMessage());
        }
    }
}
