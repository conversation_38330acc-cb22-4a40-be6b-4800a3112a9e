package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Chapter;

import java.util.List;
import java.util.Map;

/**
 * 章节服务接口
 */
public interface ChapterService extends IService<Chapter> {

    /**
     * 获取小说的章节列表（简化版，权限验证在Controller层）
     */
    List<Chapter> getChaptersByNovelId(Long novelId);

    /**
     * 获取章节详情（简化版，权限验证在Controller层）
     */
    Chapter getChapterById(Long chapterId, Long novelId);

    /**
     * 创建章节（简化版，权限验证在Controller层）
     */
    Chapter createChapter(Chapter chapter);

    /**
     * 更新章节（简化版，权限验证在Controller层）
     */
    Chapter updateChapter(Chapter chapter);

    /**
     * 删除章节（简化版，权限验证在Controller层）
     */
    boolean deleteChapter(Long chapterId, Long novelId);

    /**
     * 更新章节顺序（简化版，权限验证在Controller层）
     */
    void updateChapterOrder(Long novelId, List<Map<String, Object>> orderData);

    /**
     * 计算章节字数
     */
    int calculateWordCount(String content);

    // 保留原有方法以保持兼容性


}
